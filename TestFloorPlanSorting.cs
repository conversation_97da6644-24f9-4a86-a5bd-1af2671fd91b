using System;
using System.Collections.Generic;
using System.Linq;

namespace FloorPlanSortingTest
{
    /// <summary>
    /// 测试楼层平面图排序逻辑的独立程序
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("楼层平面图排序逻辑测试");
            Console.WriteLine("======================");

            // 测试数据：模拟各种栋号和楼层号组合
            var testData = new List<(string BuildingNumber, string FloorNumber, string GroupKey)>
            {
                ("1栋", "1层", "1栋1层平面图"),
                ("1栋", "2层", "1栋2层平面图"),
                ("1栋", "地下1层", "1栋地下1层平面图"),
                ("2栋", "1层", "2栋1层平面图"),
                ("2栋", "B1", "2栋B1平面图"),
                ("10栋", "1层", "10栋1层平面图"),
                ("A区", "1层", "A区1层平面图"),
                ("A区", "2层", "A区2层平面图"),
                ("3栋", "地下2层", "3栋地下2层平面图"),
                ("3栋", "1层", "3栋1层平面图"),
                ("3栋", "3层", "3栋3层平面图"),
                ("#1", "1层", "#1栋1层平面图"),
                ("B区", "B2", "B区B2平面图"),
                ("11栋", "1层", "11栋1层平面图")
            };

            Console.WriteLine("原始数据顺序:");
            for (int i = 0; i < testData.Count; i++)
            {
                var item = testData[i];
                Console.WriteLine($"  {i + 1}. {item.GroupKey} (栋号:{item.BuildingNumber}, 楼层:{item.FloorNumber})");
            }

            // 应用排序逻辑
            var sortedData = testData
                .OrderBy(item => ParseBuildingNumber(item.BuildingNumber))
                .ThenBy(item => ParseFloorNumber(item.FloorNumber))
                .ToList();

            Console.WriteLine("\n排序后的顺序:");
            for (int i = 0; i < sortedData.Count; i++)
            {
                var item = sortedData[i];
                int buildingSort = ParseBuildingNumber(item.BuildingNumber);
                int floorSort = ParseFloorNumber(item.FloorNumber);
                Console.WriteLine($"  {i + 1}. {item.GroupKey} (栋号:{item.BuildingNumber}[{buildingSort}], 楼层:{item.FloorNumber}[{floorSort}])");
            }

            Console.WriteLine("\n测试完成。按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 解析栋号为数字，支持混合格式
        /// </summary>
        private static int ParseBuildingNumber(string buildingNumber)
        {
            if (string.IsNullOrEmpty(buildingNumber))
                return 0;

            // 移除常见的非数字字符
            string cleanNumber = buildingNumber.Trim().Replace("栋", "").Replace("号", "").Replace("#", "");

            // 尝试解析为整数
            if (int.TryParse(cleanNumber, out int result))
            {
                return result;
            }

            // 如果无法解析为数字，使用字符串的哈希值确保一致性
            Console.WriteLine($"[DEBUG] 无法解析栋号为数字: '{buildingNumber}' -> '{cleanNumber}', 使用哈希值");
            return Math.Abs(cleanNumber.GetHashCode()) % 10000;
        }

        /// <summary>
        /// 解析楼层号为数字，支持地下室和正楼层
        /// </summary>
        private static int ParseFloorNumber(string floorNumber)
        {
            if (string.IsNullOrEmpty(floorNumber))
                return 0;

            // 移除常见的非数字字符
            string cleanNumber = floorNumber.Trim().Replace("层", "").Replace("楼", "").Replace("F", "").Replace("f", "");

            // 处理地下室标识
            bool isBasement = cleanNumber.Contains("地下") || cleanNumber.Contains("B") || cleanNumber.Contains("b");
            if (isBasement)
            {
                cleanNumber = cleanNumber.Replace("地下", "").Replace("B", "").Replace("b", "").Replace("-", "");
                if (int.TryParse(cleanNumber, out int basementLevel))
                {
                    return -basementLevel; // 地下室用负数表示，地下1层=-1，地下2层=-2
                }
            }

            // 尝试解析为整数（包括负数）
            if (int.TryParse(cleanNumber, out int result))
            {
                return result;
            }

            // 如果无法解析为数字，使用字符串的哈希值确保一致性
            Console.WriteLine($"[DEBUG] 无法解析楼层号为数字: '{floorNumber}' -> '{cleanNumber}', 使用哈希值");
            return Math.Abs(cleanNumber.GetHashCode()) % 10000;
        }
    }
}
